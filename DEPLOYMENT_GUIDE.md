# 🚀 TrustChain-Auth Deployment & Testing Guide

## 📋 Prerequisites

### **System Requirements**
- **Windows 10/11** (64-bit)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space
- **Internet**: Required for initial setup

### **Required Software**
- ✅ **Flutter SDK 3.32.4+** (Already installed)
- ✅ **Android Studio 2024.3.2+** (Already installed)
- ✅ **Python 3.8+** with virtual environment (Already configured)
- ✅ **Git** for version control

---

## 🔧 Environment Setup

### **1. Verify Flutter Installation**
```powershell
# Check Flutter version
flutter --version

# Check Flutter doctor
flutter doctor

# Expected output: All checkmarks except for optional items
```

### **2. Verify Android Environment**
```powershell
# Check Android SDK
flutter doctor --android-licenses

# Accept any pending licenses
# All licenses should be accepted
```

### **3. Verify Python ML Environment**
```powershell
# Activate virtual environment
.\venv\Scripts\Activate.ps1

# Check Python packages
pip list | findstr tensorflow
pip list | findstr scikit-learn
pip list | findstr numpy

# Expected: All ML packages installed
```

---

## 📱 Mobile App Deployment

### **1. Clean Build**
```powershell
cd mobile_app

# Clean previous builds
flutter clean

# Get dependencies
flutter pub get

# Verify no analysis issues
flutter analyze --no-fatal-infos
```

### **2. Run on Android Emulator**
```powershell
# Start Android emulator
flutter emulators --launch <emulator_name>

# Or use Android Studio to start emulator

# Run app in debug mode
flutter run

# Expected: App launches successfully
```

### **3. Run on Physical Device**
```powershell
# Enable USB debugging on Android device
# Connect device via USB

# Check device connection
flutter devices

# Run on device
flutter run -d <device_id>
```

### **4. Build Release APK**
```powershell
# Build release APK
flutter build apk --release

# APK location: build/app/outputs/flutter-apk/app-release.apk

# Install on device
flutter install --release
```

---

## 🧠 ML Models Testing

### **1. Run ML Model Tests**
```powershell
# Activate Python environment
.\venv\Scripts\Activate.ps1

# Navigate to ML models directory
cd ml_models

# Run all tests
python -m pytest tests/test_models.py -v

# Expected: 17/18 tests passing (94% success rate)
```

### **2. Test Individual Models**
```powershell
# Test Autoencoder
python -m pytest tests/test_models.py::TestBehavioralAutoencoder -v

# Test One-Class SVM
python -m pytest tests/test_models.py::TestBehavioralOneClassSVM -v

# Test Contrastive Learning
python -m pytest tests/test_models.py::TestContrastiveLearningModel -v
```

### **3. Manual ML Testing**
```python
# Create test script: test_ml_manual.py
import numpy as np
from src.models import BehavioralAutoencoder, BehavioralOneClassSVM, ContrastiveLearningModel

# Test data
X_test = np.random.random((10, 50))

# Test Autoencoder
autoencoder = BehavioralAutoencoder(input_dim=50)
autoencoder.fit(X_test)
predictions = autoencoder.predict(X_test)
print(f"Autoencoder predictions shape: {predictions.shape}")

# Test SVM
svm = BehavioralOneClassSVM()
svm.fit(X_test)
svm_predictions = svm.predict(X_test)
print(f"SVM predictions: {svm_predictions}")

# Test Contrastive Learning
contrastive = ContrastiveLearningModel(input_dim=50)
contrastive.fit(X_test)
embeddings = contrastive.get_embeddings(X_test)
print(f"Contrastive embeddings shape: {embeddings.shape}")
```

---

## 🧪 Flutter App Testing

### **1. Run Widget Tests**
```powershell
cd mobile_app

# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Expected: All tests pass
```

### **2. Run Integration Tests**
```powershell
# Run integration tests
flutter test integration_test/app_test.dart

# Expected: App flows work correctly
```

### **3. Manual App Testing**

#### **Authentication Flow**
1. **Launch App**: Should show splash screen
2. **Login Screen**: Enter credentials
3. **Behavioral Setup**: Complete onboarding
4. **Dashboard**: Access main banking interface

#### **Behavioral Data Collection**
1. **Touch Patterns**: Tap, swipe, scroll on screen
2. **Typing Dynamics**: Use search or input fields
3. **Navigation**: Move between screens
4. **Sensor Data**: Rotate device, move around

#### **Privacy Dashboard**
1. **Navigate**: Settings → Privacy
2. **View Data**: Check data usage charts
3. **Export Data**: Test JSON/CSV export
4. **Delete Data**: Test selective deletion
5. **Privacy Settings**: Toggle data collection

#### **Security Features**
1. **Risk Scoring**: Monitor risk indicators
2. **Continuous Auth**: Test background authentication
3. **Challenge System**: Trigger verification challenges
4. **Panic Gestures**: Test shake/volume button detection

---

## 🔍 Troubleshooting

### **Common Issues & Solutions**

#### **Flutter Build Errors**
```powershell
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk

# If Gradle issues persist
cd android
./gradlew clean
cd ..
flutter build apk
```

#### **Android NDK Issues**
```powershell
# Install specific NDK version
# In Android Studio: SDK Manager → NDK → Install 25.1.8937393
```

#### **Python Import Errors**
```powershell
# Reinstall packages
pip install --force-reinstall tensorflow scikit-learn numpy
```

#### **Device Connection Issues**
```powershell
# Restart ADB
adb kill-server
adb start-server
flutter devices
```

---

## 📊 Performance Testing

### **1. App Performance**
```powershell
# Profile app performance
flutter run --profile

# Monitor in Flutter Inspector
# Check memory usage, frame rates, etc.
```

### **2. ML Inference Performance**
```python
# Time ML inference
import time
import numpy as np

X_test = np.random.random((100, 50))
start_time = time.time()

# Run inference
predictions = model.predict(X_test)

end_time = time.time()
print(f"Inference time: {end_time - start_time:.3f} seconds")
print(f"Throughput: {len(X_test)/(end_time - start_time):.1f} samples/sec")
```

### **3. Memory Usage**
```powershell
# Monitor memory during app usage
flutter run --profile
# Use Flutter Inspector to monitor memory
```

---

## 🚀 Production Deployment

### **1. Backend Deployment**
```powershell
# Build Docker container
cd backend
docker build -t trustchain-auth-backend .

# Run container
docker run -p 8000:8000 trustchain-auth-backend
```

### **2. App Store Deployment**
```powershell
# Build signed APK
flutter build apk --release

# Build App Bundle for Play Store
flutter build appbundle --release

# Bundle location: build/app/outputs/bundle/release/app-release.aab
```

### **3. CI/CD Pipeline**
- Use GitHub Actions or similar
- Automated testing on commit
- Automated builds for releases
- Deployment to staging/production

---

## ✅ Verification Checklist

### **Pre-Deployment**
- [ ] All Flutter tests pass
- [ ] All ML model tests pass
- [ ] App builds without errors
- [ ] Manual testing completed
- [ ] Performance benchmarks met
- [ ] Security features verified

### **Post-Deployment**
- [ ] App installs correctly
- [ ] All features functional
- [ ] Data collection working
- [ ] ML inference operational
- [ ] Privacy controls accessible
- [ ] No crashes or errors

---

## 📞 Support

### **Getting Help**
- Check logs: `flutter logs`
- Debug mode: `flutter run --debug`
- Verbose output: `flutter run --verbose`

### **Common Commands**
```powershell
# Flutter
flutter doctor
flutter clean
flutter pub get
flutter run
flutter build apk

# Python
.\venv\Scripts\Activate.ps1
python -m pytest tests/ -v
pip list

# Android
adb devices
adb logcat
```

This guide ensures successful deployment and testing of the TrustChain-Auth application!
